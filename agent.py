# Script 4: Agentic Customer Service with Human Agent Routing (Streamlit UI)
# AI agent that can route complex complaints to human agents using tools

import os
import json
import streamlit as st
from typing import Dict, Any
from llama_index.llms.gemini import Gemini
from llama_index.core.tools import FunctionTool
from llama_index.core.agent import ReActAgent

# Set up your API key
GOOGLE_API_KEY="AIzaSyB9UVxE45gsO8QVg_0qHDFR9JUfyNP09KA"
os.environ["GOOGLE_API_KEY"] = GOOGLE_API_KEY

# Initialize Gemini LLM
llm = Gemini(
    model="models/gemini-2.5-flash-preview-05-20",
    api_key=GOOGLE_API_KEY,  # uses GOOGLE_API_KEY env var by default
)

# Initialize Streamlit session state for routing status
def init_session_state():
    if "routing_status" not in st.session_state:
        st.session_state.routing_status = {"routed_to_human": False, "ticket_id": None}
    if "messages" not in st.session_state:
        st.session_state.messages = []

def route_to_human_agent(
    customer_query: str,
    reason: str,
    priority: str = "medium",
    department: str = "general"
) -> str:
    """
    Route customer query to human agent when AI cannot handle the request.
    
    Args:
        customer_query: The original customer question/complaint
        reason: Why the query needs human attention
        priority: Priority level (low, medium, high, urgent)
        department: Which department should handle this (general, technical, billing, refunds)
    
    Returns:
        Confirmation message with ticket details
    """
    # Generate a mock ticket ID
    import random
    ticket_id = f"TICKET-{random.randint(10000, 99999)}"
    
    # Update routing status in session state
    st.session_state.routing_status["routed_to_human"] = True
    st.session_state.routing_status["ticket_id"] = ticket_id

    # Log the routing (in real implementation, this would integrate with ticketing system)
    routing_info = {
        "ticket_id": ticket_id,
        "customer_query": customer_query,
        "reason": reason,
        "priority": priority,
        "department": department,
        "status": "routed_to_human"
    }

    # Display routing information in Streamlit
    st.success(f"🎫 **ROUTING TO HUMAN AGENT** 🎫")
    st.info(f"""
    **Ticket ID:** {ticket_id}
    **Department:** {department}
    **Priority:** {priority}
    **Reason:** {reason}
    **Query:** {customer_query}
    """)
    
    return f"I've created ticket {ticket_id} and routed your request to our {department} team with {priority} priority. A human agent will contact you shortly. You'll receive an email confirmation with your ticket details."

# Create the routing tool
routing_tool = FunctionTool.from_defaults(fn=route_to_human_agent)

# Create the ReAct agent with the routing tool
agent = ReActAgent.from_tools([routing_tool], llm=llm, verbose=True)

# Custom system prompt for the agent
system_prompt = """
You are a customer service AI agent for TechSupport Inc. Your job is to help customers, but you must recognize when to route queries to human agents.

ROUTE TO HUMAN AGENT when:
1. Customer expresses strong dissatisfaction, anger, or frustration
2. Complex technical issues that require specialized knowledge
3. Billing disputes or refund requests
4. Account access issues you cannot resolve
5. Complaints about service quality
6. Requests for manager or supervisor
7. Legal or compliance matters
8. Any query you cannot adequately address

ROUTING GUIDELINES:
- Use the route_to_human_agent tool when needed
- Choose appropriate department: technical, billing, refunds, or general
- Set priority based on urgency: low, medium, high, urgent
- Provide clear reason for routing
- Once routed, do NOT continue the conversation - let human take over

For simple questions you can handle, provide helpful responses using few-shot learning patterns.

IMPORTANT: If a query has been routed to human, do not attempt to answer it further.
"""

def handle_customer_query(query: str) -> str:
    """Handle customer query with routing capability"""

    # Check if already routed to human
    if st.session_state.routing_status["routed_to_human"]:
        return f"Your request has already been routed to a human agent (Ticket: {st.session_state.routing_status['ticket_id']}). Please wait for them to contact you."

    # Create full prompt with system instructions
    full_prompt = f"{system_prompt}\n\nCustomer Query: {query}\n\nResponse:"

    # Get agent response
    response = agent.chat(full_prompt)

    return str(response)

def main():
    """Main Streamlit application"""
    st.set_page_config(
        page_title="AI Customer Service Agent",
        page_icon="🤖",
        layout="wide"
    )

    # Initialize session state
    init_session_state()

    st.title("🤖 AI Customer Service Agent")
    st.markdown("### TechSupport Inc. - Intelligent Customer Support")

    # Sidebar with information
    with st.sidebar:
        st.header("ℹ️ About")
        st.markdown("""
        This AI agent can help with:
        - General inquiries
        - Technical support
        - Billing questions
        - Account issues

        **Complex issues will be routed to human agents automatically.**
        """)

        if st.session_state.routing_status["routed_to_human"]:
            st.warning(f"🎫 **Routed to Human**\nTicket: {st.session_state.routing_status['ticket_id']}")
            if st.button("Reset Session"):
                st.session_state.routing_status = {"routed_to_human": False, "ticket_id": None}
                st.session_state.messages = []
                st.rerun()

    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])

    # Chat input
    if prompt := st.chat_input("How can I help you today?"):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})
        with st.chat_message("user"):
            st.markdown(prompt)

        # Get AI response
        with st.chat_message("assistant"):
            with st.spinner("Thinking..."):
                response = handle_customer_query(prompt)
                st.markdown(response)

                # Add assistant response to chat history
                st.session_state.messages.append({"role": "assistant", "content": response})

if __name__ == "__main__":
    main()

