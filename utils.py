import requests

def notify_human_agent(ticket_id, message):
    url = "http://localhost:5005/from_ai"
    payload = {
        "ticket_id": ticket_id,
        "message": message
    }
    response = requests.post(url, json=payload)
    print("AI -> Human:", response.json())


import requests

def send_human_reply(ticket_id, message):
    url = "http://localhost:5005/from_human"
    payload = {
        "ticket_id": ticket_id,
        "message": message
    }
    response = requests.post(url, json=payload)
    print("Human -> AI/Customer:", response.json())
